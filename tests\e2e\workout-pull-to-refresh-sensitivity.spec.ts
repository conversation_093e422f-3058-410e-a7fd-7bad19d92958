import { test, expect } from '@playwright/test'
import { login, waitForLoadingToComplete } from './helpers'

test.describe('Workout Pull-to-Refresh Sensitivity', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await page.goto('/workout')
    await waitForLoadingToComplete(page)
  })

  test('should not trigger pull-to-refresh with small scroll movements', async ({ page, context }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true
      })
    })

    // Wait for workout content to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')
    
    // Get initial state
    const refreshIndicator = page.locator('[role="progressbar"], svg.animate-spin')
    
    // Simulate small downward swipe (less than current 80px threshold)
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 160, { steps: 10 }) // 60px movement
    
    // Should not show refresh indicator with small movement
    await expect(refreshIndicator).not.toBeVisible()
    
    await page.mouse.up()
  })

  test('should require significant pull distance to trigger refresh', async ({ page, context }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true
      })
    })

    await page.waitForSelector('[data-testid="workout-overview-container"]')
    
    // Simulate touch gesture using mouse events
    await page.mouse.move(200, 100)
    await page.mouse.down()
    
    // Move down 100px (should show indicator but not trigger with new 120px threshold)
    await page.mouse.move(200, 200, { steps: 20 })
    
    // Check that pull indicator appears but doesn't trigger refresh
    const pullIndicator = page.locator('svg').filter({ hasNot: page.locator('.animate-spin') })
    await expect(pullIndicator).toBeVisible()
    
    await page.mouse.up()
    
    // Verify no refresh happened (no loading spinner)
    const loadingSpinner = page.locator('svg.animate-spin')
    await expect(loadingSpinner).not.toBeVisible()
  })

  test('should show resistance when pulling', async ({ page, context }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true
      })
    })

    await page.waitForSelector('[data-testid="workout-overview-container"]')
    
    // Start pull gesture
    await page.mouse.move(200, 50)
    await page.mouse.down()
    
    // Pull down significantly (140px raw movement)
    await page.mouse.move(200, 190, { steps: 30 })
    
    // With resistance factor 3.5, the actual pullDistance should be much less
    // We can verify by checking the transform on the indicator
    const indicator = page.locator('div').filter({ has: page.locator('svg') }).first()
    const transform = await indicator.evaluate(el => {
      const style = window.getComputedStyle(el)
      const match = style.transform.match(/translateY\((\d+)px\)/)
      return match ? parseInt(match[1]) : 0
    })
    
    // Should be significantly less than raw movement due to resistance
    expect(transform).toBeLessThan(60) // 140/3.5 ≈ 40px
    
    await page.mouse.up()
  })

  test('should not interfere with normal scrolling', async ({ page }) => {
    await page.waitForSelector('[data-testid="workout-overview-container"]')
    
    // Add content to make page scrollable
    await page.evaluate(() => {
      const container = document.querySelector('[data-testid="workout-overview-container"]')
      if (container) {
        // Add enough content to scroll
        for (let i = 0; i < 20; i++) {
          const div = document.createElement('div')
          div.style.height = '100px'
          div.textContent = `Test content ${i}`
          container.appendChild(div)
        }
      }
    })
    
    // Scroll down the page
    await page.evaluate(() => window.scrollTo(0, 200))
    
    // Try to pull down while scrolled - should not activate
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 200, { steps: 10 })
    
    // Should not show any refresh indicator when not at top
    const refreshIndicator = page.locator('div').filter({ has: page.locator('svg') }).first()
    await expect(refreshIndicator).not.toBeVisible()
    
    await page.mouse.up()
  })
})