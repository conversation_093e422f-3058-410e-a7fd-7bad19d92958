'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { RestTimer } from './RestTimer'
import { debugLog } from '@/utils/debugLog'
import { isValidExerciseId } from '@/utils/exerciseValidation'

export interface TimerScreenProps {
  soundEnabled: boolean
  vibrationEnabled: boolean
  isBetweenSets?: boolean
  restDuration?: number
}

export function TimerScreen({
  soundEnabled,
  vibrationEnabled,
  isBetweenSets = false,
  restDuration,
}: TimerScreenProps) {
  const router = useRouter()
  const {
    currentExercise,
    getNextExercise,
    currentSetIndex,
    totalSets,
    isLastSet,
    isLastExercise,
    nextSet,
    getRestDuration,
    exercises,
  } = useWorkout()

  const nextExercise = getNextExercise()

  // Redirect to workout overview if no exercise data
  useEffect(() => {
    if (!currentExercise && exercises && exercises.length === 0) {
      router.push('/workout')
    }
  }, [currentExercise, exercises, router])

  const handleTimerComplete = () => {
    // Vibrate if enabled
    if (vibrationEnabled && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100])
    }

    // Use setTimeout to ensure navigation happens after timer completion
    setTimeout(() => {
      if (isBetweenSets) {
        // For rest between sets, always go back to current exercise
        if (currentExercise?.Id && isValidExerciseId(currentExercise.Id)) {
          router.push(`/workout/exercise/${currentExercise.Id}`)
        } else {
          debugLog.error(
            'Invalid current exercise ID in timer complete:',
            currentExercise?.Id
          )
          router.push('/workout')
        }
      } else if (isLastSet && isLastExercise) {
        router.push('/workout/complete')
      } else if (isLastSet) {
        // Between exercises - nextExercise was already called before timer
        // Just navigate to the next exercise without changing state
        if (nextExercise?.Id && isValidExerciseId(nextExercise.Id)) {
          router.push(`/workout/exercise/${nextExercise.Id}`)
        } else {
          // Fallback to workout overview if no next exercise
          debugLog.error(
            'Invalid next exercise ID in timer complete:',
            nextExercise?.Id
          )
          router.push('/workout')
        }
      } else {
        // This shouldn't happen in between-exercises rest, but handle it
        nextSet()
        if (currentExercise?.Id && isValidExerciseId(currentExercise.Id)) {
          router.push(`/workout/exercise/${currentExercise.Id}`)
        } else {
          debugLog.error(
            'Invalid current exercise ID in timer else case:',
            currentExercise?.Id
          )
          router.push('/workout')
        }
      }
    }, 100) // Small delay to ensure timer state is fully updated
  }

  const handleSkip = () => {
    // Log current state for debugging
    debugLog('[TimerScreen] Skip button clicked:', {
      isBetweenSets,
      currentSetIndex,
      totalSets,
      isLastSet,
      isLastExercise,
      currentExerciseId: currentExercise?.Id,
      nextExerciseId: nextExercise?.Id,
    })

    // Vibrate if enabled
    if (vibrationEnabled && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100])
    }

    if (isBetweenSets) {
      // For rest between sets, always go back to current exercise
      if (currentExercise?.Id && isValidExerciseId(currentExercise.Id)) {
        router.push(`/workout/exercise/${currentExercise.Id}`)
      } else {
        debugLog.error(
          'Invalid current exercise ID in skip handler:',
          currentExercise?.Id
        )
        router.push('/workout')
      }
    } else if (isLastSet && isLastExercise) {
      router.push('/workout/complete')
    } else if (isLastSet) {
      // Between exercises - nextExercise was already called before timer
      // Just navigate to the next exercise without changing state
      if (nextExercise?.Id && isValidExerciseId(nextExercise.Id)) {
        router.push(`/workout/exercise/${nextExercise.Id}`)
      } else {
        // Fallback to workout overview if no next exercise
        debugLog.error(
          'Invalid next exercise ID in skip handler:',
          nextExercise?.Id
        )
        router.push('/workout')
      }
    } else {
      // This shouldn't happen in between-exercises rest, but handle it
      nextSet()
      if (currentExercise?.Id && isValidExerciseId(currentExercise.Id)) {
        router.push(`/workout/exercise/${currentExercise.Id}`)
      } else {
        debugLog.error(
          'Invalid current exercise ID in skip else case:',
          currentExercise?.Id
        )
        router.push('/workout')
      }
    }
  }

  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Rest Timer - Centered content */}
      <div className="flex-1 flex flex-col justify-center pb-24">
        <RestTimer
          onComplete={handleTimerComplete}
          onSkip={handleSkip}
          autoStart
          soundEnabled={soundEnabled}
          customDuration={restDuration ?? getRestDuration()}
        />
      </div>

      {/* Floating Skip Button */}
      <div
        data-testid="floating-skip-button"
        className="fixed bottom-6 left-0 right-0 z-50 px-4"
      >
        <div className="max-w-lg mx-auto w-full">
          <button
            onClick={handleSkip}
            className="w-full py-4 min-h-[56px] bg-gradient-metallic-gold text-text-inverse font-semibold tracking-wider rounded-theme shadow-theme-xl hover:shadow-theme-2xl hover:shadow-xl hover:shadow-brand-primary/20 active:scale-[0.98] transition-all shimmer-hover text-shadow-sm"
            aria-label="Skip rest timer"
          >
            Skip
          </button>
        </div>
      </div>
    </div>
  )
}
