'use client'

import React from 'react'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { SetCell } from './SetCell'
import { ExplainerBox } from './ExplainerBox'
import { getSetTypeFromSet } from '@/utils/getSetTypeFromSet'

// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
  }

interface ExerciseSetsGridItemProps {
  set: ExtendedWorkoutLogSerieModel
  index: number
  sets: ExtendedWorkoutLogSerieModel[]
  exercise: ExerciseModel
  recommendation?: RecommendationModel
  userBodyWeight?: number
  onSetUpdate: (
    setId: number,
    updates: {
      reps?: number
      weight?: number
    }
  ) => void
  onSetComplete?: (setIndex: number) => void
  onAddSet?: () => void
  onOneRMUpdate?: (data: {
    weight: number
    reps: number
    exercise?: {
      Id?: number
      Name?: string
      IsBodyweight?: boolean
    }
    recommendation?: {
      ExerciseId?: number
      Weight?: { Lb: number; Kg: number }
      Reps?: number
      Series?: number
      Increments?: { Lb: number; Kg: number }
    }
    isKg: boolean
    userBodyWeight: number
    isFirstWorkSet?: boolean
  }) => void
  onSaveCurrentSet?: () => void
  isSaving?: boolean
  unit?: 'kg' | 'lbs'
  currentSetIndex: number
  allSetsFinished: boolean
}

export function ExerciseSetsGridItem({
  set,
  index,
  sets,
  exercise,
  recommendation,
  userBodyWeight = 80,
  onSetUpdate,
  onSetComplete,
  onAddSet,
  onOneRMUpdate,
  onSaveCurrentSet,
  isSaving = false,
  unit = 'lbs',
  currentSetIndex,
  allSetsFinished,
}: ExerciseSetsGridItemProps) {
  const isWarmup = set.IsWarmups || false

  // Count actual warmup sets in the array
  const actualWarmupCount = sets.filter((s) => s.IsWarmups).length

  // Calculate display number: 'W1', 'W2' for warm-ups, 1-based for work sets
  let displaySetNo: string | number
  if (isWarmup) {
    // Count warmup sets to get W1, W2, W3, etc.
    const warmupIndex = sets
      .slice(0, index + 1)
      .filter((s) => s.IsWarmups).length
    displaySetNo = `W${warmupIndex}`
  } else {
    // Work sets start at 1 (subtract actual warmup count from index)
    displaySetNo = index - actualWarmupCount + 1
  }

  const isLastSet = index === sets.length - 1

  /* ------------------------------------------------------------------
   * Extract reps & weight
   * ------------------------------------------------------------------
   * A set is considered a warm-up if:
   *   • set.IsWarmups === true  OR
   *   • WarmUpWeightSet is defined (defensive for historical data)
   *
   * We prioritise warm-up fields when available, otherwise fallback to
   * the standard Reps / Weight fields. This prevents the “active set
   * mutates on focus” bug — values are read directly from the model
   * rather than recomputed.
   * ------------------------------------------------------------------ */

  // Determine if this is a warm-up using flag *or* presence of warm-up fields
  const isWarmupComputed =
    Boolean(set.IsWarmups) || set.WarmUpWeightSet !== undefined

  // Reps
  const reps = isWarmupComputed
    ? (set.WarmUpReps ?? set.Reps ?? 0)
    : (set.Reps ?? 0)

  // Weight
  let weight = 0
  if (isWarmupComputed) {
    if (set.WarmUpWeightSet) {
      weight =
        unit === 'kg'
          ? set.WarmUpWeightSet.Kg || 0
          : set.WarmUpWeightSet.Lb || 0
    }
  } else if (set.Weight) {
    weight = unit === 'kg' ? set.Weight.Kg || 0 : set.Weight.Lb || 0
  }

  // ------------------------------------------------------------------
  // Fallback: if this is a *finished* warm-up set whose stored weight is
  // zero (which happens after saving because the backend only keeps reps),
  // display the original recommendation’s warm-up weight so the value
  // doesn’t suddenly flip to 0.
  // ------------------------------------------------------------------
  if (
    isWarmupComputed &&
    weight === 0 &&
    set.IsFinished &&
    recommendation?.WarmUpsList &&
    recommendation.WarmUpsList[index]?.WarmUpWeightSet
  ) {
    const recW = recommendation.WarmUpsList[index].WarmUpWeightSet
    weight = unit === 'kg' ? recW.Kg || 0 : recW.Lb || 0
  }

  // ------------------------------------------------------------------
  // Absolute last-chance fallback: if weight is still 0 but the model
  // has a populated Weight object, display it. This covers edge-cases
  // where WarmUpWeightSet is missing yet Weight still holds the value.
  // ------------------------------------------------------------------
  if (weight === 0 && set.Weight) {
    weight = unit === 'kg' ? set.Weight.Kg || 0 : set.Weight.Lb || 0
  }

  return (
    <div
      key={set.Id || index}
      className={currentSetIndex === index ? 'ring-2 ring-brand-primary' : ''}
    >
      <SetCell
        setNo={displaySetNo}
        reps={reps}
        weight={weight}
        isFinished={set.IsFinished}
        isBodyweight={exercise.IsBodyweight}
        isLastSet={isLastSet && !allSetsFinished} // Don't show last set UI when all done
        isExerciseFinished={false} // Don't show finish button in individual cells
        isNext={set.IsNext || false}
        backColor={set.BackColor || 'transparent'}
        weightSingal={set.WeightSingal}
        setType={getSetTypeFromSet(set)}
        onRepsChange={(newReps) => {
          // Use uniform 'reps' field for both warmup and work sets
          onSetUpdate(set.Id || index + 1, { reps: newReps })
        }}
        onWeightChange={(newWeight) => {
          // Use uniform 'weight' field for both warmup and work sets
          onSetUpdate(set.Id || index + 1, { weight: newWeight })
        }}
        setData={set}
        exercise={{
          Id: exercise.Id,
          Name: exercise.Label,
          IsBodyweight: exercise.IsBodyweight,
        }}
        recommendation={
          recommendation
            ? {
                ExerciseId: recommendation.ExerciseId,
                Weight: recommendation.Weight,
                Reps: recommendation.Reps,
                Series: recommendation.Series,
                Increments: recommendation.Increments,
              }
            : undefined
        }
        userBodyWeight={userBodyWeight}
        onSetComplete={() => onSetComplete?.(index)}
        onFinishExercise={undefined} // Handle finish at grid level
        onAddSet={isLastSet && !allSetsFinished ? onAddSet : undefined}
        onOneRMUpdate={onOneRMUpdate}
        unit={unit}
      />

      {/* Show ExplainerBox and Save button under active set */}
      {set.IsNext && (
        <>
          <ExplainerBox
            recommendation={recommendation || null}
            currentSetIndex={index}
            isWarmup={set.IsWarmups || false}
            unit={unit}
            currentReps={reps}
            currentWeight={weight}
            isFirstWorkSet={
              !set.IsWarmups &&
              index === (recommendation?.WarmupsCount || actualWarmupCount)
            }
          />
          {onSaveCurrentSet && (
            <div className="px-4 pb-4">
              <button
                onClick={onSaveCurrentSet}
                disabled={isSaving}
                data-testid="floating-save-button"
                className={`w-full py-4 min-h-[56px] rounded-theme font-semibold text-lg transition-all ${
                  isSaving
                    ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
                    : 'bg-gradient-metallic-gold text-text-inverse shadow-theme-xl hover:shadow-theme-2xl active:scale-[0.98] shimmer-hover text-shadow-sm'
                }`}
              >
                {isSaving ? 'Saving...' : 'Save set'}
              </button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
