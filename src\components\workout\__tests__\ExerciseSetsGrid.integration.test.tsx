import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import type { ExerciseModel, RecommendationModel } from '@/types'

describe('ExerciseSetsGrid - Integration Tests', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsSystemExercise: true,
    BodyPartId: 1,
  }

  const mockRecommendation: RecommendationModel = {
    Id: 1,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.23 },
    WarmupsCount: 2,
    WarmUpsList: [
      { WarmUpReps: 5, WarmUpWeightSet: { Lb: 95, Kg: 43.09 } },
      { WarmUpReps: 8, WarmUpWeightSet: { Lb: 115, Kg: 52.16 } },
    ],
    HistorySet: [
      {
        Id: 1,
        Reps: 8,
        Weight: { Lb: 125, Kg: 56.7 },
        IsWarmups: false,
        SetNo: '1',
      },
    ],
    FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
    LastLogDate: '2024-01-15',
  } as RecommendationModel

  describe('Real-world Integration', () => {
    it('should show arrow buttons for active set and respond to clicks', () => {
      const mockOnSetUpdate = vi.fn()

      // Create sets with one active (IsNext: true)
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          WarmUpReps: 5,
          WarmUpWeightSet: { Lb: 95, Kg: 43 },
          Reps: 0, // Set to 0 for warmup sets
          Weight: { Lb: 0, Kg: 0 }, // Set to 0 for warmup sets
          IsNext: true, // This is the active set
          IsFinished: false,
          IsWarmups: true,
        },
        {
          Id: 2,
          SetNo: '2',
          WarmUpReps: 8,
          WarmUpWeightSet: { Lb: 115, Kg: 52 },
          Reps: 0, // Set to 0 for warmup sets
          Weight: { Lb: 0, Kg: 0 }, // Set to 0 for warmup sets
          IsNext: false,
          IsFinished: false,
          IsWarmups: true,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          recommendation={mockRecommendation}
          onSetUpdate={mockOnSetUpdate}
          unit="lbs"
        />
      )

      // Check that arrow buttons are visible for the active set
      const upArrows = screen.getAllByLabelText('Increase reps')
      const downArrows = screen.getAllByLabelText('Decrease reps')

      expect(upArrows.length).toBeGreaterThan(0)
      expect(downArrows.length).toBeGreaterThan(0)

      // Click the up arrow for reps
      fireEvent.click(upArrows[0])

      // Verify the update was called with increased reps (component uses uniform 'reps' field)
      expect(mockOnSetUpdate).toHaveBeenCalledWith(1, { reps: 6 })
    })

    it('should display ExplainerBox under active set', () => {
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 5,
          Weight: { Lb: 95, Kg: 43 },
          IsNext: false,
          IsFinished: true,
          IsWarmups: true,
        },
        {
          Id: 2,
          SetNo: '2',
          Reps: 3,
          Weight: { Lb: 115, Kg: 52 },
          IsNext: false,
          IsFinished: true,
          IsWarmups: true,
        },
        {
          Id: 3,
          SetNo: '3',
          Reps: 10,
          Weight: { Lb: 135, Kg: 61 },
          IsNext: true, // This is the active first work set
          IsFinished: false,
          IsWarmups: false,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          recommendation={mockRecommendation}
          onSetUpdate={vi.fn()}
          unit="lbs"
        />
      )

      // Check for ExplainerBox content - just verify the first work set transition text is there
      expect(screen.getByText(/warm-up.*work set/i)).toBeInTheDocument()
    })

    it('should show 1RM progress for first work set', () => {
      // Create warmup sets first, then the first work set
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 5,
          Weight: { Lb: 95, Kg: 43 },
          IsNext: false,
          IsFinished: true,
          IsWarmups: true,
        },
        {
          Id: 2,
          SetNo: '2',
          Reps: 8,
          Weight: { Lb: 115, Kg: 52 },
          IsNext: false,
          IsFinished: true,
          IsWarmups: true,
        },
        {
          Id: 3,
          SetNo: '3', // This is the first work set (index 2, after 2 warmups)
          Reps: 10,
          Weight: { Lb: 135, Kg: 61 },
          IsNext: true,
          IsFinished: false,
          IsWarmups: false,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          recommendation={mockRecommendation}
          onSetUpdate={vi.fn()}
          unit="lbs"
        />
      )

      // Check for 1RM progress display
      expect(screen.getByText(/1RM Progress:/i)).toBeInTheDocument()
    })

    it('should display Save button with gold gradient styling', () => {
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 10,
          Weight: { Lb: 135, Kg: 61 },
          IsNext: true,
          IsFinished: false,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          recommendation={mockRecommendation}
          onSetUpdate={vi.fn()}
          onSaveCurrentSet={vi.fn()}
          unit="lbs"
        />
      )

      const saveButton = screen.getByText('Save set')
      expect(saveButton).toHaveClass('bg-gradient-metallic-gold')
    })

    it('should display "No sets" message when sets array is empty', () => {
      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={[]}
          recommendation={mockRecommendation}
          onSetUpdate={vi.fn()}
          unit="lbs"
        />
      )

      expect(screen.getByText('No sets for this exercise')).toBeInTheDocument()
    })
  })
})
