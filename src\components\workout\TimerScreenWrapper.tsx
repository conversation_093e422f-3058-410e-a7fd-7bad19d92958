'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { TimerScreen } from './TimerScreen'
import { useWorkoutStore } from '@/stores/workoutStore'

export function TimerScreenWrapper() {
  const [soundEnabled, setSoundEnabled] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('soundEnabled')
      return saved !== null ? saved === 'true' : true
    }
    return true
  })

  const [vibrationEnabled, setVibrationEnabled] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('vibrationEnabled')
      return saved !== null ? saved === 'true' : true
    }
    return true
  })

  const [restDuration, setRestDuration] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('restDuration')
      if (saved !== null) {
        const duration = parseInt(saved, 10)
        if (!Number.isNaN(duration) && duration > 0) {
          return Math.max(5, Math.min(600, duration))
        }
      }
    }
    return 120 // Default 2 minutes
  })

  const searchParams = useSearchParams()
  const isBetweenSets = searchParams.get('between-sets') === 'true'
  const shouldProgress = searchParams.get('shouldProgress') === 'true'
  const nextSet = useWorkoutStore((state) => state.nextSet)

  // Progress to next set if coming from RIR selection
  useEffect(() => {
    if (shouldProgress) {
      nextSet()
    }
  }, [shouldProgress, nextSet])

  // Listen for storage changes to sync state across components
  useEffect(() => {
    const handleStorageChange = () => {
      const savedSoundPref = localStorage.getItem('soundEnabled')
      if (savedSoundPref !== null) {
        setSoundEnabled(savedSoundPref === 'true')
      }

      const savedVibrationPref = localStorage.getItem('vibrationEnabled')
      if (savedVibrationPref !== null) {
        setVibrationEnabled(savedVibrationPref === 'true')
      }

      const savedDuration = localStorage.getItem('restDuration')
      if (savedDuration !== null) {
        const duration = parseInt(savedDuration, 10)
        if (!Number.isNaN(duration) && duration > 0) {
          setRestDuration(Math.max(5, Math.min(600, duration)))
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  return (
    <TimerScreen
      soundEnabled={soundEnabled}
      vibrationEnabled={vibrationEnabled}
      isBetweenSets={isBetweenSets}
      restDuration={restDuration}
    />
  )
}
