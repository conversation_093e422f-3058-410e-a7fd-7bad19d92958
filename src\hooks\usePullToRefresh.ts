import { useEffect, useRef, useState, useCallback } from 'react'

export interface UsePullToRefreshOptions {
  /** Callback function to execute on refresh */
  onRefresh: () => Promise<void>
  /** Threshold in pixels to trigger refresh */
  threshold?: number
  /** Whether pull-to-refresh is enabled */
  enabled?: boolean
  /** Container element selector or ref */
  container?: string | React.RefObject<HTMLElement>
  /** Dead zone in pixels before pull starts registering */
  deadZone?: number
  /** Resistance factor for pull (higher = more resistance) */
  resistance?: number
}

export interface UsePullToRefreshReturn {
  /** Whether refresh is currently in progress */
  isRefreshing: boolean
  /** Current pull distance in pixels */
  pullDistance: number
  /** Whether the threshold has been reached */
  isPulling: boolean
}

export function usePullToRefresh({
  onRefresh,
  threshold = 80,
  enabled = true,
  container,
  deadZone = 0,
  resistance = 2.5,
}: UsePullToRefreshOptions): UsePullToRefreshReturn {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const [isPulling, setIsPulling] = useState(false)

  const startYRef = useRef<number | null>(null)
  const containerRef = useRef<HTMLElement | null>(null)

  // Get container element
  useEffect(() => {
    if (!enabled) return

    if (container) {
      if (typeof container === 'string') {
        containerRef.current = document.querySelector(container)
      } else if ('current' in container) {
        containerRef.current = container.current
      }
    } else {
      // Default to window scrolling
      containerRef.current = document.documentElement
    }
  }, [container, enabled])

  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      if (!enabled || isRefreshing) return

      // Check if we're at the top of the scroll container
      const scrollTop =
        window.scrollY || document.documentElement.scrollTop || 0

      // Also check custom container if provided
      const element = containerRef.current
      if (element && element !== document.documentElement) {
        const elementScrollTop = element.scrollTop || 0
        if (elementScrollTop > 0) return
      }

      if (scrollTop === 0 && e.touches[0]) {
        startYRef.current = e.touches[0].clientY
        setIsPulling(true)
      }
    },
    [enabled, isRefreshing]
  )

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!enabled || startYRef.current === null || isRefreshing) return

      const touch = e.touches[0]
      if (!touch) return

      const currentY = touch.clientY
      const distance = currentY - startYRef.current

      if (distance > 0 && isPulling) {
        // Apply dead zone - ignore movement within dead zone
        const effectiveDistance = Math.max(0, distance - deadZone)

        if (effectiveDistance > 0) {
          // Prevent default scrolling behavior only when pulling beyond dead zone
          if (e.cancelable) {
            e.preventDefault()
          }

          // Apply resistance to the pull
          const resistedDistance = Math.min(
            effectiveDistance / resistance,
            threshold * 2
          )

          setPullDistance(resistedDistance)
        } else {
          // Within dead zone, no pull distance
          setPullDistance(0)
        }
      }
    },
    [enabled, isRefreshing, threshold, isPulling, deadZone, resistance]
  )

  const handleTouchEnd = useCallback(async () => {
    if (!enabled || startYRef.current === null || isRefreshing) return

    const shouldRefresh = pullDistance >= threshold

    startYRef.current = null
    setIsPulling(false)

    if (shouldRefresh) {
      setIsRefreshing(true)
      setPullDistance(threshold) // Keep at threshold during refresh

      try {
        await onRefresh()
      } catch (error) {
        console.error('Pull to refresh error:', error)
      } finally {
        setIsRefreshing(false)
        setPullDistance(0)
      }
    } else {
      // Animate back to 0
      setPullDistance(0)
    }
  }, [enabled, isRefreshing, pullDistance, threshold, onRefresh])

  useEffect(() => {
    if (!enabled) return

    const element = containerRef.current || window

    // Add touch event listeners
    element.addEventListener('touchstart', handleTouchStart as EventListener, {
      passive: true,
    })
    element.addEventListener('touchmove', handleTouchMove as EventListener, {
      passive: false,
    })
    element.addEventListener('touchend', handleTouchEnd as EventListener, {
      passive: true,
    })

    return () => {
      element.removeEventListener(
        'touchstart',
        handleTouchStart as EventListener
      )
      element.removeEventListener('touchmove', handleTouchMove as EventListener)
      element.removeEventListener('touchend', handleTouchEnd as EventListener)
    }
  }, [enabled, handleTouchStart, handleTouchMove, handleTouchEnd])

  return {
    isRefreshing,
    pullDistance,
    isPulling,
  }
}
