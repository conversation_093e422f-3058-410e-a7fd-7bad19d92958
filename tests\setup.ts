import '@testing-library/jest-dom'
import { vi, beforeEach, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'

// Mock localStorage for tests with actual storage behavior
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value.toString()
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
    get length() {
      return Object.keys(store).length
    },
    key: vi.fn((index: number) => {
      const keys = Object.keys(store)
      return keys[index] || null
    }),
  }
})()

// eslint-disable-next-line @typescript-eslint/no-explicit-any
global.localStorage = localStorageMock as any

// Mock window.matchMedia for responsive tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock fetch for API calls to prevent connection refused errors
global.fetch = vi.fn().mockResolvedValue({
  ok: true,
  status: 200,
  json: async () => ({}),
  text: async () => '',
  headers: new Headers(),
  redirected: false,
  statusText: 'OK',
  type: 'basic',
  url: '',
  clone: () => global.fetch.mock.results[0].value,
  body: null,
  bodyUsed: false,
  arrayBuffer: async () => new ArrayBuffer(0),
  blob: async () => new Blob(),
  formData: async () => new FormData(),
} as Response)

// Mock IntersectionObserver for lazy loading tests
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
})) as any

// Mock navigator for PWA tests
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
})

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = vi.fn((cb: FrameRequestCallback): number => {
  const id = setTimeout(() => cb(Date.now()), 16)
  return id as unknown as number
})
global.cancelAnimationFrame = vi.fn((id: number) => {
  clearTimeout(id)
})

// Cleanup after each test
afterEach(() => {
  cleanup()
})

// Mock sessionStorage for OAuth tests
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
global.sessionStorage = sessionStorageMock as any

// Mock window.AppleID for Apple OAuth tests
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(window as any).AppleID = {
  auth: {
    init: vi.fn(),
    signIn: vi.fn(() => Promise.resolve({
      authorization: {
        code: 'test-auth-code',
        id_token: 'test-id-token',
        state: 'test-state',
      },
    })),
  },
}

// Mock Google Identity Services
// eslint-disable-next-line @typescript-eslint/no-explicit-any
;(window as any).google = {
  accounts: {
    id: {
      initialize: vi.fn(),
      prompt: vi.fn(),
      renderButton: vi.fn(),
      disableAutoSelect: vi.fn(),
      cancel: vi.fn(),
    },
  },
}

// Reset mocks before each test
beforeEach(() => {
  localStorageMock.getItem.mockReset()
  localStorageMock.setItem.mockReset()
  localStorageMock.removeItem.mockReset()
  localStorageMock.clear.mockReset()

  sessionStorageMock.getItem.mockReset()
  sessionStorageMock.setItem.mockReset()
  sessionStorageMock.removeItem.mockReset()
  sessionStorageMock.clear.mockReset()

  // Reset navigator.onLine to true
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(navigator as any).onLine = true
})
